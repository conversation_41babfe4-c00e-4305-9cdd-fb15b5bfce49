.DownloadDetails {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: var(--heading5);
}

.DownloadDetails .DownloadDetails__wrapper {
  border-radius: var(--border-radius);

  overflow-x: scroll;
}

.DownloadDetails .DownloadDetails__wrapper::-webkit-scrollbar {
  display: none;
}

/* Header with seller layout pattern */
.DownloadDetails .DownloadDetails__wrapper .bordrdiv {
  border-bottom: 1px solid #fddcdc;
  display: flex;
  justify-content: space-between;
}

.DownloadDetails .DownloadDetails__header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--heading5);
}

.DownloadDetails__download-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: var(--btn-color);
  color: white;
  border: none;
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.3s ease;
}

.DownloadDetails__download-btn:hover:not(:disabled) {
  background-color: var(--btn-hover-color);
  transform: translateY(-1px);
}

.DownloadDetails__download-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.DownloadDetails__download-btn .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.DownloadDetails .DownloadDetails__back-btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  background: none;
  border: none;
  color: #0a0033;
  font-size: var(--basefont);
  cursor: pointer;
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius);
  background-color: #fddcdc;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
}

.DownloadDetails .DownloadDetails__header-container h3 {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* Utility class for margin-bottom */
.DownloadDetails .mb-30 {
  margin-bottom: 30px;
}

/* Content Info */
.DownloadDetails .DownloadDetails__content-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__content-image {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.DownloadDetails .DownloadDetails__content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.DownloadDetails .DownloadDetails__content-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__content-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.DownloadDetails .DownloadDetails__content-coach {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}
.DownloadDetails .DownloadDetails__card {
  background-color: #ffffff; /* White background */
  border-radius: 16px; /* Figma border radius */
  padding: 24px; /* Figma padding */
  display: flex;
  flex-direction: column;
  gap: 24px; /* Figma gap */
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05); /* Subtle shadow */
  border: 1px solid #e0e0e0; /* Light gray border as in Figma */
}

.DownloadDetails .DownloadDetails__card-section {
  display: grid;
  gap: 20px; /* Adjusted gap */
}

.DownloadDetails .DownloadDetails__card-section--order {
  /* Adjusted for 2 main content blocks + 1 vertical line */
  grid-template-columns: 1fr;
  align-items: flex-start;
  /* margin-bottom: 24px; */ /* Gap is handled by parent */
}

.DownloadDetails .DownloadDetails__card-section--details {
  grid-template-columns: 1fr auto 1fr; /* Customer details | vertical line | Payment details */
  align-items: flex-start;
}

.DownloadDetails .DownloadDetails__card-info-block {
  display: flex;
  flex-direction: column;
  gap: 12px; /* Figma uses a slightly larger gap between subtitle and details */
  justify-content: flex-start;
}

.DownloadDetails .DownloadDetails__card-subtitle {
  font-size: 16px; /* Figma font size */
  font-weight: 600; /* Figma font weight */
  color: #0a0033; /* Figma text color */
  margin-bottom: 4px; /* Reduced margin as gap is handled by parent */
}

.DownloadDetails .DownloadDetails__card-detail {
  display: flex;
  gap: 8px;
  align-items: flex-start; /* Align items to the start for multi-line values */
  font-size: 14px; /* Figma base font size for details */
}

.DownloadDetails .DownloadDetails__card-detail-label {
  font-size: 14px; /* Figma font size */
  color: #555555; /* Figma label color (darker gray) */
  font-weight: 400; /* Figma font weight */
  min-width: 90px; /* Adjusted min-width */
  flex-shrink: 0; /* Prevent shrinking */
}

.DownloadDetails .DownloadDetails__card-detail-value {
  font-size: 14px; /* Figma font size */
  color: #0a0033; /* Figma value color */
  font-weight: 500; /* Figma font weight */
  text-align: left;
}

.DownloadDetails .DownloadDetails__card-payment {
  display: flex;
  align-items: center;
  gap: 8px; /* Figma gap */
  /* margin-top: 8px; */ /* Gap handled by parent */
}

.DownloadDetails .DownloadDetails__card-payment-icon {
  width: 30px; /* Figma icon size */
  height: 18px; /* Figma icon size */
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__card-payment-value {
  font-size: 14px; /* Figma font size */
  color: #0a0033; /* Figma value color */
  font-weight: 500; /* Figma font weight */
}

.DownloadDetails .vertical-line {
  width: 1px;
  display: flex;
  background-color: #e0e0e0; /* Figma divider color */
  min-height: 40px; /* Keep a minimum height */
  height: auto; /* Allow it to stretch based on content */
  align-self: stretch;
  margin: 0 20px; /* Figma margin for vertical line */
}

.DownloadDetails .DownloadDetails__card-divider {
  height: 1px;
  background: #e0e0e0; /* Figma divider color */
  width: 100%;
  margin: 0; /* Margin handled by parent gap */
}

/* Section Titles */
.DownloadDetails .DownloadDetails__section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--basefont) 0;
}

/* Order Information - Figma Design Match */
.DownloadDetails .DownloadDetails__order-info {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__order-info-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr auto 1fr auto 1fr;
  align-items: center;

  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__order-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
  text-align: center;
  padding: 0 var(--smallfont);
}

.DownloadDetails .DownloadDetails__order-info-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.DownloadDetails .DownloadDetails__order-info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

.DownloadDetails .DownloadDetails__order-info-divider {
  width: 1px;
  height: 40px;
  background-color: var(--light-gray);
  margin: 0 var(--smallfont);
}
.DownloadDetails .outerdivmain {
  display: grid !important;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
}

/* Customer and Payment Details Grid */
.DownloadDetails .DownloadDetails__details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading5);
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__customer-details,
.DownloadDetails .DownloadDetails__payment-details {
  padding: var(--basefont);
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__details-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.DownloadDetails .DownloadDetails__detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__detail-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.DownloadDetails .DownloadDetails__detail-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Payment Method */
.DownloadDetails .DownloadDetails__payment-method {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.DownloadDetails .DownloadDetails__payment-icon {
  width: 32px;
  height: 20px;
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__payment-text {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Video Section */
.DownloadDetails .DownloadDetails__video-section {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__video-container {
  width: 100%;
}

.DownloadDetails .DownloadDetails__video-player {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  background-color: var(--black);
}

.DownloadDetails .DownloadDetails__video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.DownloadDetails .DownloadDetails__play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.DownloadDetails .DownloadDetails__play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: rgba(238, 52, 37, 0.9);
  border: none;
  border-radius: 50%;
  color: var(--white);
  font-size: var(--heading4);
  cursor: pointer;
  transition: all 0.3s ease;
}

.DownloadDetails .DownloadDetails__play-btn:hover {
  background-color: var(--btn-color);
  transform: scale(1.1);
}

.DownloadDetails .DownloadDetails__video-title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--heading5) var(--basefont) var(--basefont);
}

.DownloadDetails .DownloadDetails__video-title {
  color: var(--white);
  font-size: var(--heading5);
  font-weight: 600;
  margin: 0;
  text-align: center;
}

/* Description */
.DownloadDetails .DownloadDetails__description-section {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__description-text {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
  margin: 0;
}

/* Error State */
.DownloadDetails .DownloadDetails__error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--error-color);
  font-size: var(--heading6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .DownloadDetails .DownloadDetails__wrapper {
    padding: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__header-container {
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__order-info-container {
    grid-template-columns: 1fr auto 1fr;
    gap: var(--smallfont);
  }
.DownloadDetails .outerdivmain {
  display: grid !important;
  grid-template-columns: 1fr;
  gap: 1rem;
}
  .DownloadDetails .DownloadDetails__order-info-item:nth-child(5),
  .DownloadDetails .DownloadDetails__order-info-divider:nth-child(4),
  .DownloadDetails .DownloadDetails__order-info-item:nth-child(7),
  .DownloadDetails .DownloadDetails__order-info-divider:nth-child(6) {
    display: none;
  }
.DownloadDetails .DownloadDetails__card-section--details {
  grid-template-columns: 1fr; /* Customer details | vertical line | Payment details */
  align-items: flex-start;
}
  .DownloadDetails .DownloadDetails__details-grid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__video-player {
    height: 250px;
  }

  .DownloadDetails .DownloadDetails__play-btn {
    width: 60px;
    height: 60px;
    font-size: var(--heading5);
  }
  .DownloadDetails .vertical-line {
display: none; /* Figma margin for vertical line */
}
}

@media (max-width: 480px) {
  .DownloadDetails .DownloadDetails__header-container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .DownloadDetails .DownloadDetails__order-info-container {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__order-info-divider {
    display: none;
  }

  .DownloadDetails .DownloadDetails__order-info-item {
    text-align: left;
    padding: var(--smallfont) 0;
    border-bottom: 1px solid var(--light-gray);
  }

  .DownloadDetails .DownloadDetails__order-info-item:last-child {
    border-bottom: none;
  }

  .DownloadDetails .DownloadDetails__content-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .DownloadDetails .DownloadDetails__video-player {
    height: 200px;
  }

  .DownloadDetails .mb-30 {
    margin-bottom: var(--basefont);
  }
}
