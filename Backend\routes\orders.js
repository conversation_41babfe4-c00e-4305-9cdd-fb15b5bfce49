const express = require('express');
const { check } = require('express-validator');
const {
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  getBuyerOrders,
  getSellerOrders,
  downloadContent
} = require('../controllers/orders');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protected routes
router.use(protect);

// User routes (accessible to all authenticated users)
router.get('/buyer', getBuyerOrders);
router.get('/:id/download', downloadContent);
router.get('/seller', getSellerOrders);

// Admin routes (keep admin-only for administrative functions)
router.get('/', authorize('admin'), getOrders);
router.put('/:id', authorize('admin'), updateOrder);

// Common routes
router.get('/:id', getOrder);

router.post(
  '/',
  [
    check('contentId', 'Content ID is required').not().isEmpty(),
    check('orderType', 'Order type is required').isIn(['Fixed', 'Auction', 'Custom'])
  ],
  createOrder
);

module.exports = router;
