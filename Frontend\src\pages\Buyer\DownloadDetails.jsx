import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { selectMyDownloads, fetchBuyerDownloads } from "../../redux/slices/buyerDashboardSlice";
import { downloadContent } from "../../redux/slices/orderSlice";
import { selectUser } from "../../redux/slices/authSlice";
import BuyerSidebar from "../../components/buyer/BuyerSidebar";
import { IoArrowBack } from "react-icons/io5";
import { FaPlay, FaDownload, FaSync, FaFilePdf, FaFileAudio, FaFileImage, FaFile } from "react-icons/fa";
import { toast } from "react-toastify";
import { getImageUrl } from "../../utils/constants";
import { downloadFileFromUrl, generateFilename } from "../../utils/downloadUtils";
import "../../styles/BuyerAccount.css";
import "../../styles/DownloadDetails.css";

const getDownloadDetails = (downloadId, downloads) => {
  const foundDownload = downloads.find(
    (download) => download._id === downloadId || download.id === downloadId
  );

  return foundDownload || null;
};

const DownloadDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const user = useSelector(selectUser);
  const [isDownloading, setIsDownloading] = useState(false);
  const download = getDownloadDetails(id, downloads);

  useEffect(() => {
    if (!downloads.length) {
      dispatch(fetchBuyerDownloads());
    }
  }, [dispatch, downloads.length]);

  const handleDownload = async () => {
    if (isDownloading || !download) return;

    setIsDownloading(true);
    try {
      const result = await dispatch(downloadContent(download._id)).unwrap();

      // Check if backend is streaming the file directly or returning a URL
      if (result.data?.isS3) {
        // S3 file - use direct download
        const downloadUrl = result.data.downloadUrl;
        const filename = generateFilename(downloadUrl, {
          title: download.title,
          fileType: download.fileType
        });

        await downloadFileFromUrl(downloadUrl, filename);
      } else {
        // Local file - backend will stream it, so we use the download endpoint directly
        const downloadEndpoint = `${import.meta.env.VITE_API_URL}/api/orders/${download._id}/download`;
        const filename = generateFilename('', {
          title: download.title,
          fileType: download.fileType
        });

        // Use blob download for local files (with authentication)
        await downloadFileFromUrl(downloadEndpoint, filename, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('xosportshub_token')}`
          }
        });
      }

      toast.success('Download started successfully!');

      // Refresh downloads to update download count
      dispatch(fetchBuyerDownloads());
    } catch (error) {
      console.error('Download error:', error);
      toast.error(error.message || 'Failed to download content. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case 'video':
        return <FaPlay className="file-icon video" />;
      case 'pdf':
      case 'document':
        return <FaFilePdf className="file-icon pdf" />;
      case 'audio':
        return <FaFileAudio className="file-icon audio" />;
      case 'image':
        return <FaFileImage className="file-icon image" />;
      default:
        return <FaFile className="file-icon default" />;
    }
  };

  if (!download) {
    return (
      <div className="BuyerAccount">
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="content">
            <div className="DownloadDetails__error">
              <p>Download not found</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const purchaseDate = new Date(download.downloadDate).toLocaleDateString();
  const thumbnailUrl = download.thumbnailUrl ? getImageUrl(download.thumbnailUrl) : null;

  return (
    <div className="BuyerAccount">
      <div className="container max-container">
        <div className="sidebar">
          <BuyerSidebar />
        </div>
        <div className="content">
          <div className="DownloadDetails">
            <div className="DownloadDetails__wrapper">
              {/* Header with seller layout pattern */}
              <div className="bordrdiv mb-30">
                <div className="DownloadDetails__header-container">
                  <button
                    className="DownloadDetails__back-btn"
                    onClick={() => navigate(-1)}
                  >
                    <IoArrowBack className="DownloadDetails__back-icon" />
                    Back
                  </button>
                  <h3>Details of Order #{download.orderId?.slice(-8) || download._id?.slice(-8)}</h3>
                  <button
                    className="DownloadDetails__download-btn"
                    onClick={handleDownload}
                    disabled={isDownloading}
                  >
                    {isDownloading ? <FaSync className="spinning" /> : <FaDownload />}
                    {isDownloading ? 'Downloading...' : 'Download Content'}
                  </button>
                </div>
              </div>

              {/* Content Info */}
              <div className="DownloadDetails__content-info">
                <div className="DownloadDetails__content-image">
                  {thumbnailUrl ? (
                    <img
                      src={thumbnailUrl}
                      alt={download.title}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div className="content-placeholder" style={{ display: thumbnailUrl ? 'none' : 'flex' }}>
                    {getFileIcon(download.fileType)}
                  </div>
                </div>
                <div className="DownloadDetails__content-details">
                  <h3 className="DownloadDetails__content-title">
                    {download.title}
                  </h3>
                  <p className="DownloadDetails__content-coach">
                    By {download.coach}
                  </p>
                  <div className="DownloadDetails__content-meta">
                    <span className="file-type">{download.fileType}</span>
                    {download.sport && <span className="sport"> • {download.sport}</span>}
                    {download.category && <span className="category"> • {download.category}</span>}
                    {download.difficulty && <span className="difficulty"> • {download.difficulty}</span>}
                  </div>
                </div>
              </div>

              <div className="DownloadDetails__card mb-40 mt-30">
                {/* Order Information */}
                <div className="DownloadDetails__card-section DownloadDetails__card-section--order">
                    <h4 className="DownloadDetails__card-subtitle">
                      Order Information
                    </h4>
               <div className="outerdivmain">
                   <div className="DownloadDetails__card-info-block">
                  
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Order Id:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        #{download.orderId?.slice(-8) || download._id?.slice(-8)}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Purchase Date:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {purchaseDate}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Downloads:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {download.downloadCount || 0} times
                      </span>
                    </div>
                  </div>
                <div className="vertical-line"></div>
                  <div className="DownloadDetails__card-info-block">
                    {/* Subtitle for this block is not needed as per Figma */}
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Content Type:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {download.fileType}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Amount Paid:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        ${download.amount?.toFixed(2) || '0.00'}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Status:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        <span className="status-badge completed">{download.status}</span>
                      </span>
                    </div>
                  </div>
               </div>
                 
                </div>
                <div className="DownloadDetails__card-divider"></div>
                {/* Customer and Payment Details */}
                <div className="DownloadDetails__card-section DownloadDetails__card-section--details">
                  <div className="DownloadDetails__card-info-block">
                    <h4 className="DownloadDetails__card-subtitle">
                      Customer Details
                    </h4>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Name:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.firstName} {user?.lastName}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Email Address:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.email}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Phone Number:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.mobile || 'Not provided'}
                      </span>
                    </div>
                  </div>
                  <div className="vertical-line" />
                  <div className="DownloadDetails__card-info-block">
                    <h4 className="DownloadDetails__card-subtitle">
                      Payment Details
                    </h4>
                    <div className="DownloadDetails__card-payment">
                      <span className="DownloadDetails__card-payment-value">
                        Payment Status: <span className="status-badge completed">{download.paymentStatus}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Preview */}
              <div className="DownloadDetails__video-section">
                <h3 className="DownloadDetails__section-title">
                  Content Preview
                </h3>
                <div className="DownloadDetails__video-container">
                  <div className="DownloadDetails__video-player">
                    {thumbnailUrl ? (
                      <img
                        src={thumbnailUrl}
                        alt="Content thumbnail"
                        className="DownloadDetails__video-thumbnail"
                      />
                    ) : (
                      <div className="DownloadDetails__content-placeholder">
                        {getFileIcon(download.fileType)}
                      </div>
                    )}
                    <div className="DownloadDetails__play-overlay">
                      <button
                        className="DownloadDetails__play-btn"
                        onClick={handleDownload}
                        disabled={isDownloading}
                      >
                        {isDownloading ? <FaSync className="spinning" /> : <FaDownload />}
                      </button>
                    </div>
                    <div className="DownloadDetails__video-title-overlay">
                      <h4 className="DownloadDetails__video-title">
                        {download.title}
                      </h4>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="DownloadDetails__description-section">
                <h3 className="DownloadDetails__section-title">Description</h3>
                <p className="DownloadDetails__description-text">
                  {download.content?.description || 'No description available.'}
                </p>
                {download.content?.strategicContent && (
                  <div className="DownloadDetails__strategic-content">
                    <h4>Strategic Content</h4>
                    <p>{download.content.strategicContent}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadDetails;
