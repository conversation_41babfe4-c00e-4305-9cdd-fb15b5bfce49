import React, { useEffect, useState, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { selectMyDownloads, fetchBuyerDownloads } from "../../redux/slices/buyerDashboardSlice";
import { downloadContent } from "../../redux/slices/orderSlice";
import { selectUser } from "../../redux/slices/authSlice";
import BuyerSidebar from "../../components/buyer/BuyerSidebar";
import { IoArrowBack } from "react-icons/io5";
import { FaPlay, FaPause, FaDownload, FaSync, FaFilePdf, FaFileAudio, FaFileImage, FaFile, FaVolumeUp, FaExpand, FaCog } from "react-icons/fa";
import { toast } from "react-toastify";
import { getImageUrl } from "../../utils/constants";
import { downloadFileFromUrl, generateFilename } from "../../utils/downloadUtils";
import "../../styles/BuyerAccount.css";
import "../../styles/DownloadDetails.css";

const getDownloadDetails = (downloadId, downloads) => {
  const foundDownload = downloads.find(
    (download) => download._id === downloadId || download.id === downloadId
  );

  return foundDownload || null;
};

const DownloadDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const user = useSelector(selectUser);
  const [isDownloading, setIsDownloading] = useState(false);
  const download = getDownloadDetails(id, downloads);

  // Video player state
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [showControls, setShowControls] = useState(true);
  const [showSpeedMenu, setShowSpeedMenu] = useState(false);

  // Debug logging
  console.log('DownloadDetails - URL ID:', id);
  console.log('DownloadDetails - Downloads length:', downloads.length);
  console.log('DownloadDetails - Downloads:', downloads);
  console.log('DownloadDetails - Found Download:', download);
  console.log('DownloadDetails - VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);
  console.log('DownloadDetails - VITE_IMAGE_BASE_URL:', import.meta.env.VITE_IMAGE_BASE_URL);
  if (downloads.length > 0) {
    console.log('DownloadDetails - First download ID:', downloads[0]._id);
    console.log('DownloadDetails - All download IDs:', downloads.map(d => d._id));
  }
  if (download) {
    console.log('DownloadDetails - Video URL will be:', `${import.meta.env.VITE_IMAGE_BASE_URL}${download.fileUrl}`);
  }

  useEffect(() => {
    if (!downloads.length) {
      dispatch(fetchBuyerDownloads());
    }
  }, [dispatch, downloads.length]);

  // Video player functions
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleSeek = (e) => {
    if (videoRef.current) {
      const rect = e.currentTarget.getBoundingClientRect();
      const pos = (e.clientX - rect.left) / rect.width;
      videoRef.current.currentTime = pos * duration;
    }
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  };

  const handleSpeedChange = (speed) => {
    setPlaybackRate(speed);
    if (videoRef.current) {
      videoRef.current.playbackRate = speed;
    }
    setShowSpeedMenu(false);
  };

  const toggleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      }
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleDownload = async () => {
    if (isDownloading || !download || !download._id) {
      console.log('Download blocked:', { isDownloading, download: !!download, downloadId: download?._id });
      return;
    }

    setIsDownloading(true);
    try {
      // Directly download from the backend endpoint with cache busting
      const timestamp = new Date().getTime();
      const downloadEndpoint = `${import.meta.env.VITE_API_BASE_URL}/orders/${download._id}/download?t=${timestamp}`;
      console.log('Download endpoint:', downloadEndpoint);

      const filename = generateFilename('', {
        title: download.title,
        fileType: download.fileType
      });

      console.log('Generated filename:', filename);

      // Get the auth token
      const token = localStorage.getItem('xosportshub_token');
      console.log('Auth token exists:', !!token);

      // Use fetch to download the file with proper headers
      const response = await fetch(downloadEndpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`Download failed: ${response.status} ${response.statusText}`);
      }

      // Check if response is JSON (S3 URL) or binary (file stream)
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        // S3 file - get the URL and download directly
        const result = await response.json();
        const downloadUrl = result.data.downloadUrl;

        // Create direct download link for S3
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Local file - convert response to blob and download
        const blob = await response.blob();
        const blobUrl = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(blobUrl);
      }

      toast.success('Download started successfully!');

      // Refresh downloads to update download count
      dispatch(fetchBuyerDownloads());
    } catch (error) {
      console.error('Download error:', error);
      toast.error(error.message || 'Failed to download content. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case 'video':
        return <FaPlay className="file-icon video" />;
      case 'pdf':
      case 'document':
        return <FaFilePdf className="file-icon pdf" />;
      case 'audio':
        return <FaFileAudio className="file-icon audio" />;
      case 'image':
        return <FaFileImage className="file-icon image" />;
      default:
        return <FaFile className="file-icon default" />;
    }
  };

  // Show loading state while downloads are being fetched
  if (!downloads.length) {
    return (
      <div className="BuyerAccount">
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="content">
            <div className="DownloadDetails__error">
              <p>Loading download details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error if download not found after downloads are loaded
  if (!download) {
    return (
      <div className="BuyerAccount">
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="content">
            <div className="DownloadDetails__error">
              <p>Download not found. ID: {id}</p>
              <button onClick={() => navigate('/buyer/downloads')}>
                Back to Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const purchaseDate = new Date(download.downloadDate).toLocaleDateString();
  const thumbnailUrl = download.thumbnailUrl ? getImageUrl(download.thumbnailUrl) : null;

  return (
    <div className="BuyerAccount">
      <div className="container max-container">
        <div className="sidebar">
          <BuyerSidebar />
        </div>
        <div className="content">
          <div className="DownloadDetails">
            <div className="DownloadDetails__wrapper">
              {/* Header with seller layout pattern */}
              <div className="bordrdiv mb-30">
                <div className="DownloadDetails__header-container">
                  <button
                    className="DownloadDetails__back-btn"
                    onClick={() => navigate(-1)}
                  >
                    <IoArrowBack className="DownloadDetails__back-icon" />
                    Back
                  </button>
                  <h3>Details of Order #{download.orderId?.slice(-8) || download._id?.slice(-8)}</h3>
                  <button
                    className="DownloadDetails__download-btn"
                    onClick={handleDownload}
                    disabled={isDownloading}
                  >
                    {isDownloading ? <FaSync className="spinning" /> : <FaDownload />}
                    {isDownloading ? 'Downloading...' : 'Download Content'}
                  </button>
                </div>
              </div>

              {/* Content Info */}
              <div className="DownloadDetails__content-info">
                <div className="DownloadDetails__content-image">
                  {thumbnailUrl ? (
                    <img
                      src={thumbnailUrl}
                      alt={download.title}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div className="content-placeholder" style={{ display: thumbnailUrl ? 'none' : 'flex' }}>
                    {getFileIcon(download.fileType)}
                  </div>
                </div>
                <div className="DownloadDetails__content-details">
                  <h3 className="DownloadDetails__content-title">
                    {download.title}
                  </h3>
                  <p className="DownloadDetails__content-coach">
                    By {download.coach}
                  </p>
                  <div className="DownloadDetails__content-meta">
                    <span className="file-type">{download.fileType}</span>
                    {download.sport && <span className="sport"> • {download.sport}</span>}
                    {download.category && <span className="category"> • {download.category}</span>}
                    {download.difficulty && <span className="difficulty"> • {download.difficulty}</span>}
                  </div>
                </div>
              </div>

              <div className="DownloadDetails__card mb-40 mt-30">
                {/* Order Information */}
                <div className="DownloadDetails__card-section DownloadDetails__card-section--order">
                    <h4 className="DownloadDetails__card-subtitle">
                      Order Information
                    </h4>
               <div className="outerdivmain">
                   <div className="DownloadDetails__card-info-block">
                  
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Order Id:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        #{download.orderId?.slice(-8) || download._id?.slice(-8)}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Purchase Date:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {purchaseDate}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Downloads:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {download.downloadCount || 0} times
                      </span>
                    </div>
                  </div>
                <div className="vertical-line"></div>
                  <div className="DownloadDetails__card-info-block">
                    {/* Subtitle for this block is not needed as per Figma */}
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Content Type:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {download.fileType}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Amount Paid:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        ${download.amount?.toFixed(2) || '0.00'}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Status:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        <span className="status-badge completed">{download.status}</span>
                      </span>
                    </div>
                  </div>
               </div>
                 
                </div>
                <div className="DownloadDetails__card-divider"></div>
                {/* Customer and Payment Details */}
                <div className="DownloadDetails__card-section DownloadDetails__card-section--details">
                  <div className="DownloadDetails__card-info-block">
                    <h4 className="DownloadDetails__card-subtitle">
                      Customer Details
                    </h4>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Name:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.firstName} {user?.lastName}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Email Address:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.email}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Phone Number:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.mobile || 'Not provided'}
                      </span>
                    </div>
                  </div>
                  <div className="vertical-line" />
                  <div className="DownloadDetails__card-info-block">
                    <h4 className="DownloadDetails__card-subtitle">
                      Payment Details
                    </h4>
                    <div className="DownloadDetails__card-payment">
                      <span className="DownloadDetails__card-payment-value">
                        Payment Status: <span className="status-badge completed">{download.paymentStatus}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Preview */}
              <div className="DownloadDetails__video-section">
                <h3 className="DownloadDetails__section-title">
                  Content Preview
                </h3>
                <div className="DownloadDetails__video-container">
                  {download.fileType?.toLowerCase() === 'video' ? (
                    <div className="DownloadDetails__video-player">
                      <div className="DownloadDetails__video-container-enhanced">
                        <video
                          ref={videoRef}
                          className="DownloadDetails__video-element"
                          poster={thumbnailUrl}
                          preload="metadata"
                          onTimeUpdate={handleTimeUpdate}
                          onLoadedMetadata={handleLoadedMetadata}
                          onPlay={() => setIsPlaying(true)}
                          onPause={() => setIsPlaying(false)}
                          onClick={togglePlay}
                        >
                          <source
                            src={`${import.meta.env.VITE_IMAGE_BASE_URL}${download.fileUrl}`}
                            type="video/mp4"
                          />
                          Your browser does not support the video tag.
                        </video>

                        {/* Custom Video Controls */}
                        {showControls && (
                          <div className="DownloadDetails__video-controls">
                            <div className="DownloadDetails__video-progress-container">
                              <div
                                className="DownloadDetails__video-progress-bar"
                                onClick={handleSeek}
                              >
                                <div
                                  className="DownloadDetails__video-progress-filled"
                                  style={{ width: `${(currentTime / duration) * 100}%` }}
                                />
                              </div>
                            </div>

                            <div className="DownloadDetails__video-controls-bottom">
                              <div className="DownloadDetails__video-controls-left">
                                <button
                                  className="DownloadDetails__video-control-btn"
                                  onClick={togglePlay}
                                >
                                  {isPlaying ? <FaPause /> : <FaPlay />}
                                </button>

                                <div className="DownloadDetails__video-volume-container">
                                  <FaVolumeUp className="DownloadDetails__video-volume-icon" />
                                  <input
                                    type="range"
                                    min="0"
                                    max="1"
                                    step="0.1"
                                    value={volume}
                                    onChange={handleVolumeChange}
                                    className="DownloadDetails__video-volume-slider"
                                  />
                                </div>

                                <div className="DownloadDetails__video-time">
                                  {formatTime(currentTime)} / {formatTime(duration)}
                                </div>
                              </div>

                              <div className="DownloadDetails__video-controls-right">
                                <div className="DownloadDetails__video-speed-container">
                                  <button
                                    className="DownloadDetails__video-control-btn"
                                    onClick={() => setShowSpeedMenu(!showSpeedMenu)}
                                  >
                                    <FaCog /> {playbackRate}x
                                  </button>

                                  {showSpeedMenu && (
                                    <div className="DownloadDetails__video-speed-menu">
                                      {[0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2].map(speed => (
                                        <button
                                          key={speed}
                                          className={`DownloadDetails__video-speed-option ${playbackRate === speed ? 'active' : ''}`}
                                          onClick={() => handleSpeedChange(speed)}
                                        >
                                          {speed}x
                                        </button>
                                      ))}
                                    </div>
                                  )}
                                </div>

                                <button
                                  className="DownloadDetails__video-control-btn"
                                  onClick={toggleFullscreen}
                                >
                                  <FaExpand />
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="DownloadDetails__video-title-overlay">
                        <h4 className="DownloadDetails__video-title">
                          {download.title}
                        </h4>
                      </div>
                    </div>
                  ) : download.fileType?.toLowerCase() === 'pdf' || download.fileType?.toLowerCase() === 'document' ? (
                    <div className="DownloadDetails__pdf-viewer">
                      <iframe
                        src={`${import.meta.env.VITE_IMAGE_BASE_URL}${download.fileUrl}`}
                        className="DownloadDetails__pdf-element"
                        title={download.title}
                      />
                      <div className="DownloadDetails__pdf-fallback">
                        <p>Cannot display PDF? <a href={`${import.meta.env.VITE_IMAGE_BASE_URL}${download.fileUrl}`} target="_blank" rel="noopener noreferrer">Open in new tab</a></p>
                      </div>
                    </div>
                  ) : download.fileType?.toLowerCase() === 'audio' ? (
                    <div className="DownloadDetails__audio-player">
                      <audio
                        controls
                        className="DownloadDetails__audio-element"
                        preload="metadata"
                      >
                        <source
                          src={`${import.meta.env.VITE_IMAGE_BASE_URL}${download.fileUrl}`}
                          type="audio/mpeg"
                        />
                        Your browser does not support the audio tag.
                      </audio>
                      <div className="DownloadDetails__audio-info">
                        <h4 className="DownloadDetails__audio-title">
                          {download.title}
                        </h4>
                        <p className="DownloadDetails__audio-coach">
                          By {download.coach}
                        </p>
                      </div>
                    </div>
                  ) : download.fileType?.toLowerCase() === 'image' ? (
                    <div className="DownloadDetails__image-viewer">
                      <img
                        src={`${import.meta.env.VITE_IMAGE_BASE_URL}${download.fileUrl}`}
                        alt={download.title}
                        className="DownloadDetails__image-element"
                      />
                      <div className="DownloadDetails__image-title-overlay">
                        <h4 className="DownloadDetails__image-title">
                          {download.title}
                        </h4>
                      </div>
                    </div>
                  ) : (
                    <div className="DownloadDetails__file-preview">
                      <div className="DownloadDetails__file-icon-large">
                        {getFileIcon(download.fileType)}
                      </div>
                      <h4 className="DownloadDetails__file-title">
                        {download.title}
                      </h4>
                      <p className="DownloadDetails__file-type">
                        {download.fileType} File
                      </p>
                      <button
                        className="DownloadDetails__preview-download-btn"
                        onClick={handleDownload}
                        disabled={isDownloading}
                      >
                        {isDownloading ? <FaSync className="spinning" /> : <FaDownload />}
                        {isDownloading ? 'Downloading...' : 'Download to View'}
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="DownloadDetails__description-section">
                <h3 className="DownloadDetails__section-title">Description</h3>
                <p className="DownloadDetails__description-text">
                  {download.content?.description || 'No description available.'}
                </p>
                {download.content?.strategicContent && (
                  <div className="DownloadDetails__strategic-content">
                    <h4>Strategic Content</h4>
                    <p>{download.content.strategicContent}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadDetails;
