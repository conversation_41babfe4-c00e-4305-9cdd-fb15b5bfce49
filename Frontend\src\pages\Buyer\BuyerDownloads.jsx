import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  selectMyDownloads,
  selectLoading,
  selectErrors,
  fetchBuyerDownloads,
  clearError
} from "../../redux/slices/buyerDashboardSlice";
import { downloadContent } from "../../redux/slices/orderSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, { TableRowSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaDownload, FaEye, FaSync, FaPlay, FaFilePdf, FaFileAudio, FaFileImage, FaFile } from "react-icons/fa";
import Table from "../../components/common/Table";
import { toast } from "react-toastify";
import { getImageUrl } from "../../utils/constants";
import { downloadFileFromUrl, generateFilename, formatFileSize } from "../../utils/downloadUtils";
import "../../styles/BuyerDownloads.css";

const BuyerDownloads = () => {
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const navigate = useNavigate();
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const [downloadingItems, setDownloadingItems] = useState(new Set());

  // Fetch downloads on component mount
  useEffect(() => {
    dispatch(fetchBuyerDownloads());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError('downloads'));
    dispatch(fetchBuyerDownloads());
  };

  const handleDownload = async (download) => {
    if (downloadingItems.has(download._id)) return;

    setDownloadingItems(prev => new Set(prev).add(download._id));
    try {
      // Directly download from the backend endpoint with cache busting
      const timestamp = new Date().getTime();
      const downloadEndpoint = `${import.meta.env.VITE_API_URL}/api/orders/${download._id}/download?t=${timestamp}`;
      const filename = generateFilename('', {
        title: download.title,
        fileType: download.fileType
      });

      // Get the auth token
      const token = localStorage.getItem('xosportshub_token');

      // Use fetch to download the file with proper headers
      const response = await fetch(downloadEndpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`Download failed: ${response.status} ${response.statusText}`);
      }

      // Check if response is JSON (S3 URL) or binary (file stream)
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        // S3 file - get the URL and download directly
        const result = await response.json();
        const downloadUrl = result.data.downloadUrl;

        // Create direct download link for S3
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Local file - convert response to blob and download
        const blob = await response.blob();
        const blobUrl = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(blobUrl);
      }

      toast.success('Download started successfully!');

      // Refresh downloads to update download count
      dispatch(fetchBuyerDownloads());
    } catch (error) {
      console.error('Download error:', error);
      toast.error(error.message || 'Failed to download content. Please try again.');
    } finally {
      setDownloadingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(download._id);
        return newSet;
      });
    }
  };

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case 'video':
        return <FaPlay className="file-icon video" />;
      case 'pdf':
      case 'document':
        return <FaFilePdf className="file-icon pdf" />;
      case 'audio':
        return <FaFileAudio className="file-icon audio" />;
      case 'image':
        return <FaFileImage className="file-icon image" />;
      default:
        return <FaFile className="file-icon default" />;
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "orderId",
      label: "Order Id",
      className: "order-id",
    },
    {
      key: "content",
      label: "Content",
      className: "content",
    },
    {
      key: "date",
      label: "Purchase Date",
      className: "date",
    },
    {
      key: "amount",
      label: "Amount",
      className: "amount",
    },
    {
      key: "downloads",
      label: "Downloads",
      className: "downloads",
    },
    {
      key: "actions",
      label: "Actions",
      className: "actions",
    },
  ];

  const renderRow = (download, index) => {
    const isDownloading = downloadingItems.has(download._id);
    const thumbnailUrl = download.thumbnailUrl ? getImageUrl(download.thumbnailUrl) : null;
    const purchaseDate = new Date(download.downloadDate).toLocaleDateString();

    return (
      <>
        <div className="table-cell no">{index + 1}</div>
        <div className="table-cell order-id">#{download.orderId?.slice(-8) || download._id?.slice(-8)}</div>
        <div className="table-cell content">
          <div className="content-item">
            <div className="content-image">
              {thumbnailUrl ? (
                <img
                  src={thumbnailUrl}
                  alt={download.title}
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
              ) : null}
              <div className="content-placeholder" style={{ display: thumbnailUrl ? 'none' : 'flex' }}>
                {getFileIcon(download.fileType)}
              </div>
            </div>
            <div className="content-info">
              <div className="content-title">{download.title}</div>
              <div className="content-coach">By {download.coach}</div>
              <div className="content-meta">
                <span className="file-type">{download.fileType}</span>
                {download.fileSize && (
                  <span className="file-size"> • {formatFileSize(download.fileSize)}</span>
                )}
                {download.duration && (
                  <span className="duration"> • {formatDuration(download.duration)}</span>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="table-cell date">{purchaseDate}</div>
        <div className="table-cell amount">${download.amount?.toFixed(2) || '0.00'}</div>
        <div className="table-cell downloads">
          <span className="download-count">{download.downloadCount || 0} times</span>
          {download.lastDownloaded && (
            <div className="last-download">
              Last: {new Date(download.lastDownloaded).toLocaleDateString()}
            </div>
          )}
        </div>
        <div className="table-cell actions">
          <button
            className={`action-btn download-btn ${isDownloading ? 'downloading' : ''}`}
            onClick={() => handleDownload(download)}
            disabled={isDownloading}
            title="Download content"
          >
            {isDownloading ? <FaSync className="spinning" /> : <FaDownload />}
          </button>
          <button
            className="action-btn view-btn"
            onClick={() => navigate(`/buyer/download-details/${download._id}`)}
            title="View details"
          >
            <FaEye />
          </button>
        </div>
      </>
    );
  };

  return (
    <div className="BuyerDownloads">
      <SectionWrapper
        icon={<FaDownload className="BuyerSidebar__icon" />}
        title="My Downloads"
        action={
          errors.downloads && (
            <button
              className="retry-btn"
              onClick={handleRetry}
              title="Retry loading downloads"
            >
              <FaSync />
            </button>
          )
        }
      >
        {errors.downloads ? (
          <ErrorDisplay
            error={errors.downloads}
            onRetry={handleRetry}
            title="Failed to load downloads"
          />
        ) : loading.downloads ? (
          <div className="loading-container">
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
          </div>
        ) : downloads.length > 0 ? (
          <Table
            columns={columns}
            data={downloads}
            renderRow={renderRow}
            variant="grid"
            gridTemplate="0.5fr 1fr 3fr 1.2fr 1fr 1.2fr 1fr"
            className="BuyerDownloads__table"
            emptyMessage="You have no downloads yet."
          />
        ) : (
          <div className="BuyerDownloads__empty">
            <h3>No downloads yet</h3>
            <p>Your purchased content will appear here once you make your first purchase.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerDownloads;
